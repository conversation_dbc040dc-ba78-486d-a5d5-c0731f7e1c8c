import 'package:eljunto/reusableWidgets/transition_widget.dart';
import 'package:eljunto/views/login/login_screen.dart';
import 'package:eljunto/views/login/name_handle_screen.dart';
import 'package:eljunto/views/login/otp_screen.dart';
import 'package:eljunto/views/login/set_password.dart';
import 'package:eljunto/views/login/sign_up.dart';
import 'package:eljunto/views/login/signup_final_step.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Defines the authentication-related routes for the application.
class AuthRoutes {
  AuthRoutes._();

  static final List<RouteBase> routes = [
    GoRoute(
      path: '/login',
      name: 'login',
      pageBuilder: (context, state) {
        return CustomTransitionPage(
          key: state.pageKey,
          child: const LoginPage(),
          transitionDuration: const Duration(milliseconds: 950),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;
            final tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );
            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
      },
    ),
    GoRoute(
      path: '/sign-up',
      name: 'sign-up',
      pageBuilder: (context, state) {
        final extras = state.extra as Map;
        return TransitionPageWidget.navigateTransitionPage(
          child: SignUpPage(
            isForgotPassword: extras['isForgotPassword'],
          ),
        );
      },
    ),
    GoRoute(
      path: '/otp',
      name: 'otp',
      pageBuilder: (context, state) {
        final Map<String, dynamic>? extra =
            state.extra as Map<String, dynamic>?;
        final email = extra?['email'] as String?;
        final token = extra?['token'] as String?;
        return TransitionPageWidget.navigateTransitionPage(
          child: OTPPage(
            email: email ?? '',
            token: token ?? '',
            isForgotPassword: extra?['isForgotPassword'],
          ),
        );
      },
    ),
    GoRoute(
      path: '/set-password',
      name: 'set-password',
      pageBuilder: (context, state) {
        final Map<String, dynamic>? extra =
            state.extra as Map<String, dynamic>?;
        final email = extra?['email'] as String?;
        final token = extra?['token'] as String?;
        final otp = extra?['otp'] as String?;
        return TransitionPageWidget.navigateTransitionPage(
          child: SetPasswordPage(
            email: email ?? '',
            token: token ?? '',
            otp: otp ?? '',
            isForgotPassword: extra?['isForgotPassword'],
          ),
        );
      },
    ),
    GoRoute(
      path: '/set-name-handle',
      name: 'set-name-handle',
      pageBuilder: (context, state) {
        final Map<String, dynamic>? extra =
            state.extra as Map<String, dynamic>?;
        final email = extra?['email'] as String?;
        final token = extra?['token'] as String?;
        final otp = extra?['otp'] as String?;
        return TransitionPageWidget.navigateTransitionPage(
          child: NameAndHandlePage(
            email: email ?? '',
            token: token ?? '',
            otp: otp ?? '',
          ),
        );
      },
    ),
    GoRoute(
      path: '/profilesetup',
      name: 'profilesetup',
      pageBuilder: (context, state) {
        return TransitionPageWidget.navigateTransitionPage(
          child: ProfileSetupFinalStepPage(
            key: state.pageKey,
          ),
        );
      },
    ),
  ];
}
